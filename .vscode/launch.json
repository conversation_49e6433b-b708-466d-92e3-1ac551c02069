{
  "version": "0.2.0",
  "configurations": [
      {
          "name": "Launch web",
          "type": "chrome",
          "request": "launch",
          "url": "http://localhost:3010",
          "pathMapping": {
            "/": "${workspaceFolder}/src",
            "@fs/Users/<USER>/Desktop/docsOscar/pitsDepot/pd-monorepo/pd-storybook/": "${workspaceFolder}/../pd-storybook"
          },
          "webRoot": "${workspaceFolder}/src",
          "sourceMaps": true,
          "userDataDir": "${workspaceFolder}/.vscode/chrome",
          "internalConsoleOptions": "neverOpen",
      },
  ]
}
