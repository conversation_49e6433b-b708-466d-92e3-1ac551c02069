{
    "version": "2.0.0",
    "tasks": [
      {
        "label": "startServer",
        "type": "npm",
        "script": "start:dev",
        "isBackground": true,
        "problemMatcher": {
          "owner": "javascript",
          "pattern": {
            "regexp": ".",
            "file": 1,
            "location": 2,
            "message": 3
          },
          "background": {
            "activeOnStart": true,
            "beginsPattern": "^.*ready in.*ms$",  // Mensaje cuando Vite comienza a inicializar
            "endsPattern": "^.*Local:.*$"        // Mensaje que indica que Vite está listo
          }
        },
        "presentation": {
          "reveal": "always",
          "panel": "new"
        },
        "group": {
          "kind": "build",
          "isDefault": true
        }
      },
      {
        "label": "killServer",
        "type": "shell",
        "command": "pkill -SIGTERM  -f 'genia-web/node_modules/.bin/../vite/bin/vite.js'",
        "problemMatcher": [],
        "presentation": {
          "reveal": "silent"
        }
      }
    ]
  }