version: 2.1
orbs:
  slack: circleci/slack@5.1.1

parameters:
  tag:
    type: string
    default: ""

commands:
  send-slack-message:
    parameters:
      title:
        type: string
      event: 
        type: enum
        enum:
          - always
          - pass
          - fail
      workflow_name:
        type: string
    steps:
      - slack/notify:
          custom: |
            {
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "Genia Web: << parameters.title >>",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Project:*\n${CIRCLE_PROJECT_REPONAME}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Branch:*\n${CIRCLE_BRANCH}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Tag:*\n<< pipeline.parameters.tag >>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Workflow name/Build Id:*\n<< parameters.workflow_name >>/${CIRCLE_WORKFLOW_ID}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Triggered by:*\n${CIRCLE_USERNAME:-None}"
                    }
                  ]
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":link: *Links*"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "<${CIRCLE_BUILD_URL}|View Build Details>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${REPO_URL}/commits/${CIRCLE_SHA1}|See commit>"
                    }
                  ]
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "This notification was generated by CircleCI for ${CIRCLE_PROJECT_REPONAME}."
                    }
                  ]
                }
              ]
            }
          event: << parameters.event >>
          channel: "#ci_cd"
jobs:
  build-and-deploy:
    environment:
      REPO_URL: << pipeline.project.git_url >>
    docker:
      - image: cimg/node:20.18.1
    parameters:
      workflow_name:
        type: string
    steps:
      - send-slack-message:
          title: ":hourglass_flowing_sand: Deploy in progress"
          event: always
          workflow_name: << parameters.workflow_name >>
      - checkout
      - run: 
          name: Checkout to tag
          command: |
            git fetch --all --tags
            git checkout tags/<< pipeline.parameters.tag >>
      - run:
          name: Write GOOGLE_APPLICATION_CREDENTIALS to file
          command: |
            echo "$DEPLOY_CREDENTIALS" > /tmp/google-credentials.json
            echo  "export GOOGLE_APPLICATION_CREDENTIALS=/tmp/google-credentials.json" >> $BASH_ENV
      - restore_cache:
          name: Restore pnpm Package Cache
          keys:
            - pnpm-packages-{{ checksum "pnpm-lock.yaml" }}
      - run:
          name: Install pnpm package manager
          command: |
            npm install -g corepack@latest
            corepack enable
            corepack prepare pnpm@latest-10 --activate
            pnpm config set store-dir .pnpm-store
            pnpm config set global-bin-dir .pnpm-store/.bin
            echo "export PATH=$PATH:$(pwd)/.pnpm-store/.bin" >> $BASH_ENV
      - run:
          name: Authenticate with private registry
          command: |
            echo "$DEPLOY_CREDENTIALS" > /tmp/google-credentials.json
            export GOOGLE_APPLICATION_CREDENTIALS=/tmp/google-credentials.json
            pnpm artifactregistry-login
      - run:
          name: Install Dependencies
          command: pnpm install --frozen-lockfile --ignore-scripts
      - run:
          name: Install Firebase Tools
          command: pnpm install -g firebase-tools
      - save_cache:
          name: Save pnpm Package Cache
          key: pnpm-packages-{{ checksum "pnpm-lock.yaml" }}
          paths:
            - .pnpm-store
      - run:
          name: Build Project
          command: pnpm build
      - run:
          name: Deploy to Firebase
          command: firebase deploy --only hosting --project genia-web-production
      - send-slack-message:
          title: ":rocket: Deployed to production"
          event: pass
          workflow_name: << parameters.workflow_name >>
      - send-slack-message:
          title: ":bangbang: Deploy failed :speak_no_evil:"
          event: fail
          workflow_name: << parameters.workflow_name >>
workflows:
  build-deploy:
    when: << pipeline.parameters.tag >>
    jobs:
      - build-and-deploy:
          context:
            - CREDENTIALS
            - SLACK
          workflow_name: "Build and Deploy"